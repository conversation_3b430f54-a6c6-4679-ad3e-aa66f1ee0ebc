/**
 * 滴答清单基础 API 封装云函数
 * 提供统一的 HTTP 请求封装和认证管理
 * 
 * 功能模块：
 * - makeRequest: 统一的 HTTP 请求封装
 * - getToken: Token 获取和管理
 * - validateToken: Token 有效性验证
 * - handleError: 统一错误处理
 * - formatResponse: 响应数据标准化
 */

const BASE_URL = 'https://api.dida365.com/open/v1'

module.exports = {
  _before: function () {
    // 通用预处理器
    console.log('dida-base 云函数调用开始')
  },

  /**
   * 统一的 HTTP 请求封装
   * @param {object} params 参数对象
   * @param {string} params.url 请求 URL
   * @param {string} params.method 请求方法
   * @param {object} [params.data] 请求数据
   * @param {object} [params.headers] 自定义请求头
   * @param {string} [params.token] 访问令牌
   * @returns {object} 请求响应
   */
  makeRequest: async function(params) {
    console.log('--- 调用 makeRequest ---', params)
    const { url, method, data, headers = {}, token } = params || {}
    
    if (!url || !method) {
      return {
        errCode: 'INVALID_PARAMS',
        errMsg: 'URL 和请求方法都是必需的参数'
      }
    }
    
    try {
      // 1. 获取访问令牌
      const accessToken = token || await this.getToken()
      if (!accessToken) {
        return {
          errCode: 'TOKEN_ERROR',
          errMsg: '无法获取有效的访问令牌'
        }
      }
      
      // 2. 构建请求头（参考 MCP 实现格式）
      const requestHeaders = {
        'Accept': 'application/json',
        'Cookie': `t=${accessToken}`,
        'User-Agent': 'Apifox/1.0.0',
        'Content-Type': 'application/json',
        'Host': 'api.dida365.com',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        ...headers
      }
      
      // 3. 构建完整 URL
      const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`
      
      // 4. 发送请求
      const response = await uniCloud.httpclient.request(fullUrl, {
        method: method.toUpperCase(),
        headers: requestHeaders,
        data: data,
        dataType: 'json',
        timeout: 30000 // 30 秒超时
      })
      
      console.log('makeRequest 成功，响应数据：', response.data)
      
      return this.formatResponse(response.data)
    } catch (error) {
      console.error('makeRequest 失败：', error)
      return this.handleError(error)
    }
  },
  
  /**
   * 获取访问令牌
   * @param {object} [params] 参数对象
   * @param {boolean} [params.forceRefresh] 是否强制刷新
   * @returns {string} 访问令牌
   */
  getToken: async function(params = {}) {
    const { forceRefresh = false } = params
    
    try {
      // 1. 检查缓存的令牌
      if (!forceRefresh) {
        const cachedToken = await this.getCachedToken()
        if (cachedToken && await this.validateToken({ token: cachedToken })) {
          return cachedToken
        }
      }
      
      // 2. 使用预设令牌（实际项目中可能需要动态获取）
      const token =
        '3B033D35C0941BAC9E252C1ED3D43388D858E8AFEAAB2390DE89A15B13AEBF6EC99722A66E87409C19B634FAF8B61BCB66D74CB1A5C00FDD94BE739FD776C7F4804D8EB8613305E973532E9BF4ABD72377FC271889007EB032442C06676583676E249547BDA6444B06DC86A33A59910E151002FFD8A5114164D117B92EA2F106DAC825256333498B6B459F5B7D2A570D30C018A70CFD503A6AAC03FF0B09EE61C0ABBFBE4C30F683D7CF82F24CA79A3B4C4FFBD3D1FB387E'
      
      // 3. 验证令牌有效性
      const isValid = await this.validateToken({ token })
      if (!isValid) {
        throw new Error('预设令牌无效')
      }
      
      // 4. 缓存令牌
      await this.cacheToken(token)
      
      return token
    } catch (error) {
      console.error('getToken 失败：', error)
      return null
    }
  },
  
  /**
   * 验证令牌有效性
   * @param {object} params 参数对象
   * @param {string} params.token 要验证的令牌
   * @returns {boolean} 是否有效
   */
  validateToken: async function(params) {
    console.log('validateToken 开始执行，参数：', params)
    const { token } = params || {}

    if (!token) {
      console.log('validateToken：token 为空，返回 false')
      return false
    }

    console.log('validateToken：token 存在，长度：', token.length)
    console.log('validateToken：准备调用 API，URL：', `${BASE_URL}/user/profile`)

    try {
      // 通过调用一个简单的 API 来验证令牌
      const response = await uniCloud.httpclient.request(`${BASE_URL}/user/profile`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Cookie': `t=${token}`,
          'User-Agent': 'Apifox/1.0.0',
          'Content-Type': 'application/json',
          'Host': 'api.dida365.com'
        },
        dataType: 'json',
        timeout: 10000
      })

      console.log('validateToken：API 调用成功，状态码：', response.status)
      console.log('validateToken：响应数据：', JSON.stringify(response.data))

      const isValid = response.status === 200
      console.log('validateToken：验证结果：', isValid)
      return isValid
    } catch (error) {
      console.error('validateToken 失败：', error)
      console.error('validateToken 错误详情：', {
        message: error.message,
        status: error.status,
        statusCode: error.statusCode
      })
      return false
    }
  },

  /**
   * 统一错误处理
   * @param {Error} error 错误对象
   * @returns {object} 标准化错误响应
   */
  handleError: function(error) {
    console.error('处理错误：', error)

    // 根据不同类型的错误返回不同的错误码
    if (error.code === 'TIMEOUT') {
      return {
        errCode: 'REQUEST_TIMEOUT',
        errMsg: '请求超时，请稍后重试',
        error: error.message
      }
    }

    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      if (status === 401) {
        return {
          errCode: 'UNAUTHORIZED',
          errMsg: '认证失败，请检查访问令牌',
          error: data
        }
      }

      if (status === 403) {
        return {
          errCode: 'FORBIDDEN',
          errMsg: '权限不足，无法访问该资源',
          error: data
        }
      }

      if (status === 404) {
        return {
          errCode: 'NOT_FOUND',
          errMsg: '请求的资源不存在',
          error: data
        }
      }

      if (status >= 500) {
        return {
          errCode: 'SERVER_ERROR',
          errMsg: '服务器内部错误，请稍后重试',
          error: data
        }
      }
    }

    return {
      errCode: 'UNKNOWN_ERROR',
      errMsg: error.message || '未知错误',
      error: error
    }
  },

  /**
   * 标准化响应数据格式
   * @param {any} data 原始响应数据
   * @returns {object} 标准化响应
   */
  formatResponse: function(data) {
    return {
      success: true,
      data: data,
      timestamp: new Date().toISOString()
    }
  },

  /**
   * 获取缓存的令牌
   * @returns {string|null} 缓存的令牌
   */
  getCachedToken: async function() {
    try {
      const db = uniCloud.database()
      const collection = db.collection('dida_token_cache')

      const result = await collection
        .where({
          type: 'access_token',
          expireTime: db.command.gt(new Date())
        })
        .orderBy('createTime', 'desc')
        .limit(1)
        .get()

      if (result.data && result.data.length > 0) {
        console.log('从缓存获取到有效令牌')
        return result.data[0].token
      }

      console.log('缓存中没有有效令牌')
      return null
    } catch (error) {
      console.error('获取缓存令牌失败：', error)
      return null
    }
  },

  /**
   * 缓存令牌
   * @param {string} token 要缓存的令牌
   * @param {number} [expireHours=24] 过期时间（小时）
   */
  cacheToken: async function(token, expireHours = 24) {
    try {
      const db = uniCloud.database()
      const collection = db.collection('dida_token_cache')

      // 计算过期时间
      const expireTime = new Date()
      expireTime.setHours(expireTime.getHours() + expireHours)

      // 先删除旧的令牌记录
      await collection.where({
        type: 'access_token'
      }).remove()

      // 插入新的令牌记录
      await collection.add({
        type: 'access_token',
        token: token,
        createTime: new Date(),
        expireTime: expireTime
      })

      console.log('令牌缓存成功，过期时间：', expireTime.toISOString())
    } catch (error) {
      console.error('缓存令牌失败：', error)
    }
  },

  /**
   * 获取标准请求头
   * @param {string} token 访问令牌
   * @param {object} customHeaders 自定义请求头
   * @returns {object} 完整的请求头
   */
  getHeaders: function(token, customHeaders = {}) {
    return {
      'Accept': 'application/json',
      'Cookie': `t=${token}`,
      'User-Agent': 'Apifox/1.0.0',
      'Content-Type': 'application/json',
      'Host': 'api.dida365.com',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      ...customHeaders
    }
  }
}
